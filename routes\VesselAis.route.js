const express = require("express");
const { validateData } = require("../middlewares/validator");
const { body, query } = require("express-validator");
const { validateError, getUnitIdsFromVessel, canAccessVessel } = require("../utils/functions");
const limitPromise = require("../modules/pLimit");
const { default: mongoose } = require("mongoose");
const isAuthenticated = require("../middlewares/auth");
const { default: rateLimit } = require("express-rate-limit");
const assignEndpointId = require("../middlewares/assignEndpointId");
const { endpointIds } = require("../utils/endpointIds");
const db = require("../modules/db");
const router = express.Router();
const compression = require("compression");
const vesselService = require("../services/Vessel.service");

const aisProjection = {
    _id: 1,
    location: 1,
    "metadata.message": 1,
    name: 1,
    mmsi: 1,
    timestamp: 1,
};

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);
router.use(compression());

const getRandomDeviation = () => {
    return Math.random() * 0.001;
};

router.post(
    "/:vesselId",
    assignEndpointId.bind(this, endpointIds.FETCH_VESSEL_AIS),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                // the below cannot be verified by test cases
                // param("vesselId").isMongoId().withMessage("Invalid vessel ID"),
                body("startTimestampISO")
                    .isISO8601()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
                body("endTimestampISO")
                    .isISO8601()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
            ],
            req,
            res,
            next,
        ),
    async (req, res) => {
        const requestURL = req.get("Referer");
        const isSwagger = requestURL ? requestURL.includes("/docs") : false;
        let isClosed = false;

        const onClose = () => {
            isClosed = true;
        };

        res.on("close", onClose);

        try {
            const { vesselId } = req.params;
            const { startTimestampISO, endTimestampISO, lastKnown } = req.body;
            console.log(`/vesselAis ${vesselId}`, startTimestampISO, endTimestampISO, lastKnown);

            if (!vesselId || !mongoose.Types.ObjectId.isValid(vesselId)) return res.status(400).json({ message: "Invalid vessel ID" });

            if (endTimestampISO && !startTimestampISO) {
                return res.status(400).json({ message: "startTimestampISO is required when endTimestampISO is provided" });
            }

            const vessel = await vesselService.findById({ id: vesselId });
            if (!vessel) return res.status(404).json({ message: "Vessel does not exist" });

            if (!canAccessVessel(req, vessel)) {
                return res.status(403).json({ message: `Cannot access coordinates for vessel '${vesselId}'` });
            }

            const historicalUnitIds = getUnitIdsFromVessel(vessel);
            if (historicalUnitIds.length === 0) return res.status(400).json({ message: "Vessel has no associated unit_id in history" });

            console.log(`/vesselAis ${vesselId} historicalUnitIds`, historicalUnitIds);

            const ts = new Date().getTime();

            const collections = historicalUnitIds.map((unitId) => db.ais.collection(`${unitId}_ais`));

            // console.log(`/vesselAis ${vesselId} collections`, collections)

            const query = { onboard_vessel_id: mongoose.Types.ObjectId(vesselId) };

            if (startTimestampISO) {
                const endTime = endTimestampISO || Date.now();
                query.timestamp = { $gte: new Date(startTimestampISO), $lte: new Date(endTime) };
            }

            console.log(`/vesselAis ${vesselId} query`, query);

            const ais = await limitPromise(async () => {
                if (isClosed) return res.end();
                console.log(`/vesselAis ${vesselId} querying DB`);

                if (lastKnown) {
                    const aisPromises = collections.map((collection) =>
                        collection.findOne(
                            { onboard_vessel_id: mongoose.Types.ObjectId(vesselId) },
                            {
                                projection: aisProjection,
                                sort: { timestamp: -1 },
                            },
                        ),
                    );

                    const allAis = (await Promise.all(aisPromises)).filter(Boolean);

                    if (allAis.length === 0) return null;

                    return allAis.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())[0];
                } else {
                    const aisPromises = collections.map((collection) => {
                        const cursor = collection.find(query, {
                            projection: aisProjection,
                        });

                        if (isSwagger) {
                            cursor.limit(20);
                        }

                        return cursor.toArray();
                    });

                    const allAis = await Promise.all(aisPromises);
                    const flattenedResults = allAis
                        .flat()
                        // temporary randomization logic for mock data
                        .map((ais) => ({
                            ...ais,
                            location: ais.location && {
                                ...ais.location,
                                coordinates: [ais.location.coordinates[0] + getRandomDeviation(), ais.location.coordinates[1] + getRandomDeviation()],
                            },
                        }));

                    return flattenedResults.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
                }
            });

            console.log(`/vesselAis ${vesselId} time taken to query ${new Date().getTime() - ts}`);
            console.log(`/vesselAis ${vesselId} received ${(ais && ais.length) || 0} ais`);
            console.log(`/vesselAis ${vesselId} time taken to respond ${new Date().getTime() - ts}`);

            if (isClosed) return res.end();

            res.json(ais);
        } catch (err) {
            validateError(err, res);
        } finally {
            res.removeListener("close", onClose);
        }
    },
);

router.get(
    "/latest",
    assignEndpointId.bind(this, endpointIds.FETCH_VESSEL_AIS_LATEST),
    isAuthenticated,
    validateData.bind(this, [
        // param("vesselId").isMongoId().withMessage("Invalid vessel ID"),
        query("startTimestampISO")
            .isISO8601()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
            .optional(),
        // not supported at the moment
        // query("endTimestamp")
        //     .isInt()
        //     .customSanitizer((v) => Number(v))
        //     .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
        //     .optional(),
    ]),
    async (req, res) => {
        const requestURL = req.get("Referer");
        const isSwagger = requestURL ? requestURL.includes("/docs") : false;
        let isClosed = false;

        const onClose = () => {
            isClosed = true;
        };

        res.on("close", onClose);

        try {
            const { startTimestampISO } = req.query;

            const ts = new Date().getTime();

            const query = {};

            if (startTimestampISO) {
                query.last_message_timestamp = { $gte: new Date(startTimestampISO) };
            }

            console.log(`/vesselAis query`, query);

            const ais = await limitPromise(async () => {
                if (isClosed) return res.end();
                const ts = new Date().getTime();

                console.log(`/vesselAis querying DB`);

                const cursor = db.lookups.collection("ais_mmsi_lookup").find(query);

                if (isSwagger) {
                    cursor.limit(20);
                }

                const ais = await cursor.toArray();

                const lookupCollectionIds = {};

                ais.forEach((ais) => {
                    if (!lookupCollectionIds[ais.collection]) lookupCollectionIds[ais.collection] = [];
                    lookupCollectionIds[ais.collection].push(ais.last_message_id);
                });

                console.log(`/vesselAis lookupCollectionIds`, lookupCollectionIds);

                const aisMessages = (
                    await Promise.all(
                        Object.entries(lookupCollectionIds).flatMap(([collection, ids]) => {
                            return db.ais
                                .collection(collection)
                                .find({ "metadata._id": { $in: ids } }, { projection: aisProjection })
                                .toArray();
                        }),
                    )
                ).flat();

                // console.log('aisMessages', aisMessages);
                console.log(`/vesselAis time taken to query ${new Date().getTime() - ts}`);

                return aisMessages;
            });

            console.log(`/vesselAis received ${(ais && ais.length) || 0} ais`);
            console.log(`/vesselAis time taken to respond ${new Date().getTime() - ts}`);

            if (isClosed) return res.end();

            res.json(ais);
        } catch (err) {
            validateError(err, res);
        } finally {
            res.removeListener("close", onClose);
        }
    },
);

module.exports = router;
