const express = require("express");
const { validateData } = require("../middlewares/validator");
const { body, query } = require("express-validator");
const { validateError, canAccessVessel, getUnitIdsFromVessel, splitByMonthsUTC } = require("../utils/functions");
const limitPromise = require("../modules/pLimit");
const { default: mongoose, isValidObjectId } = require("mongoose");
const isAuthenticated = require("../middlewares/auth");
const { default: rateLimit } = require("express-rate-limit");
const assignEndpointId = require("../middlewares/assignEndpointId");
const { endpointIds } = require("../utils/endpointIds");
const db = require("../modules/db");
const router = express.Router();
const compression = require("compression");
const vesselService = require("../services/Vessel.service");
const vesselLocationService = require("../services/VesselLocation.service");

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);
router.use(compression());

router.post(
    "/:vesselName",
    assignEndpointId.bind(this, endpointIds.FETCH_COORDINATES),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                // the below cannot be verified by test cases
                // param('vesselName').isString().notEmpty().withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                body("lastKnown")
                    .customSanitizer((v) => Number(v))
                    .isInt({ min: 0, max: 1 })
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
                body("startTimestamp")
                    .isInt()
                    .customSanitizer((v) => Number(v))
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
                body("endTimestamp")
                    .isInt()
                    .customSanitizer((v) => Number(v))
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
                body("excludeIds")
                    .isArray()
                    .bail()
                    .customSanitizer((v) => v.map((id) => mongoose.Types.ObjectId(id)))
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
            ],
            req,
            res,
            next,
        ),
    async (req, res) => {
        const requestURL = req.get("Referer");
        const isSwagger = requestURL ? requestURL.includes("/docs") : false;
        let isClosed = false;

        const onClose = () => {
            isClosed = true;
        };

        res.on("close", onClose);

        try {
            const ts = new Date().getTime();
            const { vesselName } = req.params;
            const { startTimestamp, endTimestamp, lastKnown, excludeIds } = req.body;
            console.log(`/vesselLocations ${vesselName}`, startTimestamp, endTimestamp, lastKnown);

            if (endTimestamp && !startTimestamp) {
                return res.status(400).json({ message: "startTimestamp is required when endTimestamp is provided" });
            }

            const vessel = await vesselService.findByAssignedUnitId({ unitId: vesselName });
            if (!vessel) return res.status(404).json({ message: "Vessel does not exist" });

            if (!canAccessVessel(req, vessel)) {
                return res.status(403).json({ message: `Cannot access coordinates for '${vesselName}'` });
            }

            const historicalUnitIds = getUnitIdsFromVessel(vessel);
            if (historicalUnitIds.length === 0) {
                return res.status(400).json({ message: "Vessel has no associated unit_id in history" });
            }

            const collections = historicalUnitIds.map((unitId) => db.locations.collection(`${unitId}_location`));
            if (!collections.length) return res.status(400).json({ message: "Invalid vesselName" });

            const query = {};
            if (startTimestamp) {
                const endTime = endTimestamp || Date.now();
                query.timestamp = { $gt: new Date(startTimestamp), $lt: new Date(endTime) };
            }
            if (excludeIds) query._id = { $nin: excludeIds };

            var locations = await limitPromise(async () => {
                if (isClosed) return res.end();
                console.log(`/vesselLocations ${vesselName} querying DB`);
                if (lastKnown) {
                    const locations = (
                        await Promise.all(
                            collections.map((collection) =>
                                collection.findOne(
                                    {},
                                    {
                                        projection: { _id: 1, latitude: 1, longitude: 1, groundSpeed: 1, isStationary: 1, timestamp: 1 },
                                        sort: { timestamp: -1 },
                                    },
                                ),
                            ),
                        )
                    )
                        .flat()
                        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
                    return locations[0];
                } else {
                    const locations = (
                        await Promise.all(
                            collections.map((collection) => {
                                const cursor = collection.find(query, {
                                    projection: { _id: 1, latitude: 1, longitude: 1, groundSpeed: 1, isStationary: 1, timestamp: 1 },
                                });

                                if (isSwagger) {
                                    cursor.limit(20);
                                }

                                return cursor.toArray();
                            }),
                        )
                    )
                        .flat()
                        .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
                    return locations;
                }
            });

            console.log(`/vesselLocations ${vesselName} time taken to query ${new Date().getTime() - ts}`);
            console.log(`/vesselLocations ${vesselName} received ${(locations && locations.length) || 1} coordinates`);
            console.log(`/vesselLocations ${vesselName} time taken to respond ${new Date().getTime() - ts}`);

            if (isClosed) return res.end();

            res.json(locations);
        } catch (err) {
            validateError(err, res);
        } finally {
            res.removeListener("close", onClose);
        }
    },
);

router.get(
    "/bulk",
    assignEndpointId.bind(this, endpointIds.FETCH_COORDINATES_BULK),
    isAuthenticated,
    validateData.bind(this, [
        query("vesselIds")
            .isString()
            .withMessage(`vesselIds is a required string`)
            .notEmpty()
            .withMessage(`vesselIds must be a comma-separated string`)
            .if(query("vesselIds").exists())
            .customSanitizer((v) => v.split(",").map((v) => v.trim()))
            .custom((v) => v.every((id) => isValidObjectId(id)))
            .withMessage(`vesselIds must be valid object IDs`),
        query("startTimestampISO").isISO8601().withMessage(`startTimestampISO must be a valid ISO 8601 timestamp`).optional(),
        query("endTimestampISO").isISO8601().withMessage(`endTimestampISO must be a valid ISO 8601 timestamp`).optional(),
    ]),
    async (req, res) => {
        // const requestURL = req.get("Referer");
        // const isSwagger = requestURL ? requestURL.includes("/docs") : false;
        let isClosed = false;

        const onClose = () => {
            isClosed = true;
        };

        res.on("close", onClose);

        try {
            const ts = new Date().getTime();
            const { vesselIds, startTimestampISO, endTimestampISO } = req.query;
            console.log(`/vesselLocations/bulk ${vesselIds}`, startTimestampISO, endTimestampISO);

            if (endTimestampISO && !startTimestampISO) {
                return res.status(400).json({ message: "startTimestampISO is required when endTimestamp is provided" });
            }

            const vessels = await vesselService.find({ _id: { $in: vesselIds } });

            const assignedVessels = vessels.filter((vessel) => canAccessVessel(req, vessel));

            const query = {};
            if (startTimestampISO) {
                const endTime = endTimestampISO || Date.now();
                query.timestamp = { $gt: new Date(startTimestampISO), $lt: new Date(endTime) };
            }

            const locations = await limitPromise(async () => {
                // const allLocations = {};

                console.log(`/vesselLocations/bulk querying DB`);
                const ts = new Date().getTime();

                const vesselIds = assignedVessels.map((vessel) => vessel._id.toString());
                const dateRanges = splitByMonthsUTC(startTimestampISO, endTimestampISO);
                const locations = (
                    await Promise.all(
                        dateRanges.map(async (range) =>
                            vesselLocationService.findByDateRange({
                                dateRange: [range.start, range.end],
                                vesselIds,
                                projection: { _id: 1, location: 1, isStationary: 1, timestamp: 1, "metadata.onboardVesselId": 1 },
                            }),
                        ),
                    )
                ).flat();

                const groupedLocations = locations.reduce((acc, loc) => {
                    const vesselId = loc.metadata.onboardVesselId.toString();
                    if (!acc[vesselId]) {
                        acc[vesselId] = [];
                    }
                    acc[vesselId].push([loc._id, loc.timestamp, loc.location.coordinates[1], loc.location.coordinates[0], loc.isStationary]);
                    return acc;
                }, {});

                assignedVessels.forEach((vessel) => {
                    const vesselId = vessel._id.toString();
                    if (!groupedLocations[vesselId]) {
                        groupedLocations[vesselId] = [];
                    }
                });

                // await Promise.all(
                //     assignedVessels.map(async (vessel) => {
                //         const currentVesselId = vessel._id.toString();

                //         // const historicalUnitIds = getUnitIdsFromVessel(vessel);
                //         // if (historicalUnitIds.length === 0) {
                //         //     allLocations[currentVesselId] = [];
                //         //     return;
                //         // }

                //         // console.log(`/vesselLocations/bulk historicalUnitIds ${historicalUnitIds}`);

                //         const collections =[ db.locationsOptimized.collection(`2025-07`)];

                //         if (isClosed) return res.end();
                //         const vesselLocations = (
                //             await Promise.all(
                //                 collections.map(async (collection) => {
                //                     const ts = new Date().getTime();
                //                     const cursor = collection.find(
                //                         { ...query, 'metadata.onboardVesselId': new mongoose.Types.ObjectId(currentVesselId) },
                //                         {
                //                             projection: { _id: 1, latitude: 1, longitude: 1, isStationary: 1, timestamp: 1 },
                //                         },
                //                     );

                //                     if (isSwagger) {
                //                         cursor.limit(20);
                //                     }

                //                     const result = (await cursor.toArray()).map(obj => ([obj._id, obj.timestamp, obj.latitude, obj.longitude, obj.isStationary]));
                //                     // const explain = await cursor.explain();
                //                     // console.log(explain);
                //                     console.log(
                //                         `/vesselLocations/bulk ${currentVesselId} time taken to query collection ${collection.name} ${new Date().getTime() - ts}`,
                //                     );
                //                     return result;
                //                 }),
                //             )
                //         )
                //             .flat()
                //             .sort((a, b) => new Date(a[1]).getTime() - new Date(b[1]).getTime());

                //         allLocations[currentVesselId] = vesselLocations;
                //     }),
                // );
                console.log(`/vesselLocations/bulk time taken to query ${new Date().getTime() - ts}`);

                return groupedLocations;
            });

            // console.log(`/vesselLocations/bulk received ${(locations && locations.length) || 1} coordinates`);
            console.log(`/vesselLocations/bulk time taken to respond ${new Date().getTime() - ts}`);

            if (isClosed) return res.end();

            res.json(locations);
        } catch (err) {
            validateError(err, res);
        } finally {
            res.removeListener("close", onClose);
        }
    },
);

module.exports = router;

/**
 * @swagger
 * tags:
 *   name: Vessel Locations
 *   description: Fetch vessel location data
 * components:
 *   schemas:
 *     VesselLocation:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Unique identifier for the vessel location
 *           example: "66e75fc080f445d4f062b294"
 *         latitude:
 *           type: number
 *           format: float
 *           description: Latitude of the vessel's location
 *           example: 8.333152
 *         longitude:
 *           type: number
 *           format: float
 *           description: Longitude of the vessel's location
 *           example: 117.2075473
 *         groundSpeed:
 *           type: number
 *           format: float
 *           description: The ground speed of the vessel in knots
 *           example: 0.023
 *         timestamp:
 *           type: string
 *           format: date-time
 *           description: Timestamp when the location data was recorded
 *           example: "2024-09-15T22:29:20.555Z"
 *         isStationary:
 *           type: boolean
 *           description: Indicates whether the vessel is stationary at this location
 *           example: false
 */

/**
 * @swagger
 * /vesselLocations/{vesselName}:
 *   post:
 *     summary: Fetch vessel location data. (This route is deprecated, use v2 instead)
 *     description: Fetch vessel location data for a given vessel, with optional parameters for filtering by timestamp range, excluding specific IDs, and fetching the last known location.<br/>Rate limited to 20 requests every 5 seconds
 *     tags: [Vessel Locations]
 *     deprecated: true
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: vesselName
 *         required: true
 *         description: Name of the vessel to fetch location data for
 *         schema:
 *           type: string
 *           example: prototype-37
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               lastKnown:
 *                 type: integer
 *                 required: false
 *                 description: Flag to only fetch last known location (0 for false, 1 for true)
 *                 example: 0
 *               startTimestamp:
 *                 type: integer
 *                 required: false
 *                 description: Start unix timestamp in milliseconds for filtering location data
 *                 example: 1727136000000
 *               endTimestamp:
 *                 type: integer
 *                 required: false
 *                 description: End unix timestamp in milliseconds for filtering location data
 *                 example: 1727222400000
 *               excludeIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 required: false
 *                 description: Array of document IDs to exclude from the result
 *                 example: ["64c6b0f7f83c8b57fa5f3242", "64c6b0f7f83c8b57fa5f3243"]
 *     responses:
 *       200:
 *         description: An array of vessel location coordinates
 *         content:
 *           text/html:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/VesselLocation'
 *       400:
 *         description: Invalid request or vessel name
 *       403:
 *         description: Cannot access this unit
 *       500:
 *         description: Internal server error
 */

router.post(
    "/:vesselId/closest",
    assignEndpointId.bind(this, endpointIds.FETCH_COORDINATES_CLOSEST),
    isAuthenticated,
    validateData.bind(this, [body("timestampISO").isISO8601().withMessage("timestampISO must be a valid ISO 8601 date")]),
    async (req, res) => {
        try {
            const { vesselId } = req.params;
            const { timestampISO } = req.body;

            const vessel = await vesselService.findById({ id: vesselId });
            if (!vessel) return res.status(404).json({ message: "Vessel does not exist" });

            if (!canAccessVessel(req, vessel)) {
                return res.status(403).json({ message: "Cannot access coordinates for this vessel" });
            }

            const targetTimestamp = new Date(timestampISO).getTime();

            // Find the specific unit ID that was active during the requested timestamp
            const activeUnitId = vessel.units_history.find((unitData) => {
                const mountTime = unitData.mount_timestamp ? new Date(unitData.mount_timestamp).getTime() : 0;
                const unmountTime = unitData.unmount_timestamp ? new Date(unitData.unmount_timestamp).getTime() : Date.now();
                return targetTimestamp >= mountTime && targetTimestamp <= unmountTime;
            });

            if (!activeUnitId) {
                return res.status(404).json({ message: "No unit was active during the specified timestamp" });
            }

            const timeWindow = 60 * 1000; // 1 minute
            const searchQuery = {
                onboardVesselId: mongoose.Types.ObjectId(vesselId),
                timestamp: {
                    $gte: new Date(targetTimestamp - timeWindow),
                    $lte: new Date(targetTimestamp + timeWindow),
                },
            };

            // Query only the specific collection for the active unit
            const collection = db.locations.collection(`${activeUnitId.unit_id}_location`);
            const result = await collection
                .aggregate([
                    { $match: searchQuery },
                    { $addFields: { timeDiff: { $abs: { $subtract: [{ $toLong: "$timestamp" }, targetTimestamp] } } } },
                    { $sort: { timeDiff: 1 } },
                    { $limit: 1 },
                    { $project: { _id: 1, latitude: 1, longitude: 1, timestamp: 1, groundSpeed: 1 } },
                ])
                .toArray();

            const closestLocation = result.length > 0 && result[0].latitude && result[0].longitude ? result[0] : null;

            if (!closestLocation) {
                return res.status(404).json({ message: "No coordinate found" });
            }

            res.json(closestLocation);
        } catch (err) {
            validateError(err, res);
        }
    },
);

module.exports = router;
