import { Grid, Typography, CircularProgress, Box } from "@mui/material";
import { memo, useRef, useCallback, useState, useEffect, forwardRef } from "react";
import VirtualizedList from "./VirtualizedList";
import { SentimentVeryDissatisfied } from "@mui/icons-material";
import VirtualizedCardListRow from "./VirtualizedCardListRow";
import { useApp } from "../../../hooks/AppHook";
import theme from "../../../theme";

const VirtualizedCardList = forwardRef(
    (
        {
            events,
            setShowDetailModal,
            setSelectedCard,
            favouriteArtifacts,
            isLoading,
            onLoadMore,
            hasMore,
            containerRef,
            CustomCard,
            onFlaggedByClick,
            buttonsToShow,
        },
        ref,
    ) => {
        const { screenSize } = useApp();
        const listRef = useRef();
        const [isScrolling, setIsScrolling] = useState(false);
        const [containerHeight, setContainerHeight] = useState(0);

        useEffect(() => {
            if (ref) {
                ref.current = {
                    scrollToTop: () => {
                        if (listRef.current) {
                            listRef.current.scrollTo(0);
                        }
                    },
                };
            }
        }, [ref]);

        useEffect(() => {
            const target = containerRef?.current;
            if (!target) return;

            const updateHeight = () => {
                const newHeight = target.clientHeight;
                setContainerHeight(isLoading ? newHeight - 70 : newHeight);
            };

            updateHeight();
            const resizeObserver = new ResizeObserver(updateHeight);
            resizeObserver.observe(target);

            return () => {
                resizeObserver.unobserve(target);
            };
        }, [containerRef, isLoading]);

        useEffect(() => {
            if (listRef.current) {
                listRef.current.resetAfterIndex(0);
            }
        }, [events, screenSize]);

        const getColumnCount = useCallback(() => {
            if (screenSize.xs) return 1;
            if (screenSize.sm) return 2;
            if (screenSize.md) return 3;
            if (screenSize.lg) return 4;
            return 5;
        }, [screenSize]);

        const getItemSize = (index) => {
            const columnCount = getColumnCount();
            const rowIndex = Math.floor(index / columnCount);
            return rowIndex === 0 ? 350 : 350;
        };

        const checkScrollPosition = useCallback((scrollOffset, clientHeight, scrollHeight) => {
            const scrollPosition = scrollOffset + clientHeight;
            return scrollPosition >= scrollHeight * 0.8;
        }, []);

        const handleScroll = useCallback(
            ({ scrollOffset, scrollUpdateWasRequested }) => {
                if (!isLoading && onLoadMore && !scrollUpdateWasRequested && listRef.current) {
                    const { scrollHeight, clientHeight } = listRef.current._outerRef;

                    if (checkScrollPosition(scrollOffset, clientHeight, scrollHeight) && hasMore && !isLoading && !isScrolling) {
                        setIsScrolling(true);
                        onLoadMore();
                        setTimeout(() => setIsScrolling(false), 1000);
                    }
                }
            },
            [hasMore, isLoading, onLoadMore, isScrolling, checkScrollPosition],
        );

        if (isLoading && events.length === 0) {
            return (
                <Grid display={"flex"} justifyContent={"center"} alignItems={"center"} size={12}>
                    <CircularProgress />
                </Grid>
            );
        }

        if (events.length === 0) {
            return (
                <Grid display={"flex"} justifyContent={"center"} alignItems={"center"} size={12}>
                    <Grid display={"flex"} flexDirection={"column"} alignItems={"center"} justifyContent={"center"}>
                        <SentimentVeryDissatisfied sx={{ fontSize: "100px", color: theme.palette.custom.borderColor }} />
                        <Typography variant="h6" component="div" gutterBottom color={theme.palette.custom.borderColor}>
                            No data available
                        </Typography>
                    </Grid>
                </Grid>
            );
        }

        const columnCount = getColumnCount();

        return (
            <>
                <VirtualizedList
                    listRef={listRef}
                    containerHeight={containerHeight}
                    getItemSize={getItemSize}
                    handleScroll={handleScroll}
                    Row={VirtualizedCardListRow}
                    items={events}
                    columnCount={columnCount}
                    rowData={{
                        CustomCard,
                        setShowDetailModal,
                        setSelectedCard,
                        favouriteArtifacts,
                        onFlaggedByClick,
                        buttonsToShow,
                    }}
                />
                {isLoading && events.length > 0 && (
                    <Box display="flex" justifyContent="center" width={"100%"} padding={2}>
                        <CircularProgress />
                    </Box>
                )}
            </>
        );
    },
);

VirtualizedCardList.displayName = "VirtualizedCardList";
export default memo(VirtualizedCardList);
