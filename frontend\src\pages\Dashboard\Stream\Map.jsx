import { IconButton } from "@mui/material";
import { OpenInNew } from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import InsetMap from "../../../components/InsetMap";
import { useApp } from "../../../hooks/AppHook";

const Map = ({ vessel, newCoordinate, streamMode }) => {
    const { setSelectedVessel } = useApp();
    const navigate = useNavigate();

    const handleButtonClick = () => {
        setSelectedVessel(vessel.vesselId);
        navigate("/dashboard/map");
    };

    return (
        <>
            <InsetMap vessel={vessel} newCoordinate={newCoordinate} initialZoom={7} streamMode={streamMode} />
            <IconButton onClick={handleButtonClick} sx={{ position: "absolute", bottom: 25, left: 1, zIndex: 0 }} disableRipple>
                <OpenInNew sx={{ color: "#000" }} />
            </IconButton>
        </>
    );
};

export default Map;
